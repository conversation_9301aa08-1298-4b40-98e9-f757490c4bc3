$(document).ready(function(){
    // تحسين معالجة الأخطاء
    $('form#login input').on('change', function(){
        $(this).closest('.form-group').removeClass('has-error');
        $(this).siblings('.help-block').text('');

        // إزالة تأثير الاهتزاز
        $(this).removeClass('shake');
    });

    // تحسين إرسال النموذج
    $('form#login').on('submit', function(e){
        e.preventDefault();
        e.stopImmediatePropagation();

        var infobox = $('#infoMessage');
        var btnsubmit = $('#submit');

        // تأثير التحميل المحسن
        showLoadingState(btnsubmit, infobox);

        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: $(this).serialize(),
            success: function(data){
                hideLoadingState(btnsubmit);

                if(data.status){
                    // حفظ بيانات تسجيل الدخول في localStorage
                    saveLoginData();

                    // رسالة نجاح محسنة
                    showSuccessMessage(infobox);

                    // انتقال سلس
                    setTimeout(function() {
                        var go = base_url + data.url;
                        window.location.href = go;
                    }, 1500);

                } else {
                    handleLoginErrors(data, infobox);
                }
            },
            error: function() {
                hideLoadingState(btnsubmit);
                showErrorMessage(infobox, 'حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.');
            }
        });
    });

    // وظائف مساعدة محسنة
    function showLoadingState(btn, infobox) {
        btn.attr('disabled', 'disabled');
        btn.html('<i class="fas fa-spinner fa-spin" style="margin-left: 8px;"></i> جاري التحقق...');
        btn.addClass('loading');

        infobox.removeClass().addClass('callout callout-info text-center');
        infobox.html('<i class="fas fa-circle-notch fa-spin" style="margin-left: 8px;"></i> جاري التحقق من البيانات...');
    }

    function hideLoadingState(btn) {
        btn.removeAttr('disabled');
        btn.html('<i class="fas fa-sign-in-alt" style="margin-left: 8px;"></i> تسجيل الدخول');
        btn.removeClass('loading');
    }

    function saveLoginData() {
        var identity = $('[name="identity"]').val();
        var password = $('[name="password"]').val();
        var savedData = JSON.parse(localStorage.getItem('savedData')) || [];

        var existingData = savedData.find(data => data.identity === identity);
        if (existingData) {
            existingData.password = password;
        } else {
            savedData.push({
                identity: identity,
                password: password
            });
        }
        localStorage.setItem('savedData', JSON.stringify(savedData));
    }

    function showSuccessMessage(infobox) {
        infobox.removeClass().addClass('callout callout-success text-center');
        infobox.html('<i class="fas fa-check-circle" style="margin-left: 8px;"></i> تم تسجيل الدخول بنجاح! جاري التوجيه...');

        // تأثير بصري للنجاح
        $('.login-box-body').addClass('success-animation');
    }

    function showErrorMessage(infobox, message) {
        infobox.removeClass().addClass('callout callout-danger text-center');
        infobox.html('<i class="fas fa-exclamation-triangle" style="margin-left: 8px;"></i> ' + message);
    }

    function handleLoginErrors(data, infobox) {
        if(data.invalid){
            $.each(data.invalid, function(key, val){
                var field = $('[name="'+key+'"]');
                var formGroup = field.closest('.form-group');

                if(val && val !== ''){
                    formGroup.addClass('has-error');
                    field.siblings('.help-block').text(val);

                    // تأثير اهتزاز للحقل
                    field.addClass('shake');
                    setTimeout(function() {
                        field.removeClass('shake');
                    }, 500);
                } else {
                    formGroup.removeClass('has-error');
                    field.siblings('.help-block').text('');
                }
            });
        }

        if(data.failed){
            showErrorMessage(infobox, data.failed);

            // تأثير اهتزاز للحاوية
            $('.login-box-body').addClass('error-shake');
            setTimeout(function() {
                $('.login-box-body').removeClass('error-shake');
            }, 500);
        }
    }

    // إضافة CSS للتأثيرات
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .loading {
                background: linear-gradient(135deg, #95a5a6, #7f8c8d) !important;
                cursor: not-allowed !important;
            }

            .success-animation {
                animation: successPulse 0.6s ease;
            }

            @keyframes successPulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }

            .error-shake {
                animation: errorShake 0.5s ease;
            }

            @keyframes errorShake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-10px); }
                75% { transform: translateX(10px); }
            }

            .shake {
                animation: inputShake 0.5s ease;
            }

            @keyframes inputShake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }
        `)
        .appendTo('head');
});