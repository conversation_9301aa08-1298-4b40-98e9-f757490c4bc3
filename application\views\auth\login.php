<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام الامتحانات الذكي</title>
    <link rel="stylesheet" href="<?=base_url()?>assets/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            width: 100%;
            height: 100%;
            font-family: 'Cairo', sans-serif;
            direction: rtl;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* خلفية متحركة */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .login-container {
            position: relative;
            z-index: 10;
            width: 100%;
            max-width: 450px;
            margin: 0 auto;
            padding: 20px;
        }

        .login-box-body {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 40px 35px;
            border-radius: 20px;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .login-box-body:hover {
            transform: translateY(-5px);
            box-shadow:
                0 35px 60px rgba(0, 0, 0, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
        }

        .login-box-body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
            border-radius: 20px 20px 0 0;
        }

        .login-header {
            text-align: center;
            margin-bottom: 35px;
        }

        .login-header h3 {
            color: #2c3e50;
            font-weight: 700;
            font-size: 28px;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .login-header .subtitle {
            color: #7f8c8d;
            font-size: 16px;
            font-weight: 400;
            margin-bottom: 25px;
        }

        .login-box-msg {
            text-align: center;
            color: #34495e;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 25px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 12px;
            border: 1px solid rgba(0,0,0,0.05);
        }

        /* تصميم الحقول الحديث */
        .form-label {
            color: #2c3e50;
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 8px;
            display: block;
        }

        .input-group {
            position: relative;
            margin-bottom: 10px;
        }

        .input-group-text {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 12px 0 0 12px;
            padding: 12px 15px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .modern-input {
            border: 2px solid #e9ecef;
            border-radius: 0 12px 12px 0;
            padding: 12px 15px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            color: #2c3e50;
        }

        .modern-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
            outline: none;
        }

        .modern-select {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 10px 15px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .modern-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            outline: none;
        }

        .toggle-password {
            border: 2px solid #e9ecef;
            border-left: none;
            border-radius: 0 12px 12px 0;
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
            transition: all 0.3s ease;
        }

        .toggle-password:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .custom-control-label {
            color: #2c3e50;
            font-weight: 500;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .custom-control-label:hover {
            color: #667eea;
        }

        .modern-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 15px 30px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .modern-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .modern-btn:hover::before {
            left: 100%;
        }

        .modern-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .modern-btn:active {
            transform: translateY(0);
        }

        .login-footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid rgba(0,0,0,0.1);
        }

        /* رسائل التنبيه */
        .callout {
            border-radius: 12px;
            padding: 15px 20px;
            margin-bottom: 20px;
            border: none;
            font-weight: 500;
        }

        .callout-info {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .callout-success {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
        }

        .callout-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .login-container {
                padding: 15px;
            }

            .login-box-body {
                padding: 30px 25px;
                margin: 10px;
            }

            .login-header h3 {
                font-size: 24px;
            }

            .modern-input, .toggle-password {
                padding: 10px 12px;
                font-size: 14px;
            }

            .modern-btn {
                padding: 12px 25px;
                font-size: 14px;
            }
        }

        @media (max-width: 480px) {
            .login-header h3 {
                font-size: 20px;
            }

            .login-box-msg {
                font-size: 16px;
                padding: 10px 15px;
            }
        }

        /* تأثيرات الأخطاء */
        .has-error .modern-input {
            border-color: #e74c3c;
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .help-block {
            color: #e74c3c;
            font-size: 12px;
            margin-top: 5px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-box-body">
            <div class="login-header">
                <h3>
                    <i class="fas fa-graduation-cap" style="color: #667eea; margin-left: 10px;"></i>
                    نظام الامتحانات الذكي
                </h3>
                <p class="subtitle">SMART Examination System</p>
            </div>

            <p class="login-box-msg">
                <i class="fas fa-sign-in-alt" style="margin-left: 8px; color: #667eea;"></i>
                تسجيل الدخول
            </p>

            <div id="infoMessage" class="text-center mb-3"><?php echo $message;?></div>

            <?= form_open("auth/cek_login", array('id'=>'login'));?>

            <div class="form-group mb-4">
                <label class="form-label">البريد الإلكتروني</label>
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">
                            <i class="fas fa-envelope"></i>
                        </span>
                    </div>
                    <input type="text" name="identity" id="identity" class="form-control modern-input"
                           placeholder="أدخل بريدك الإلكتروني" autocomplete="off">
                </div>

                <!-- قائمة اختيار البريد الإلكتروني -->
                <select id="savedIdentitiesSelect" class="form-control modern-select mt-2" style="display: none;">
                    <option value="">-- اختر بريد إلكتروني محفوظ --</option>
                </select>

                <span class="help-block"></span>
            </div>

            <div class="form-group mb-4">
                <label class="form-label">كلمة المرور</label>
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                    </div>
                    <input type="password" name="password" id="password" class="form-control modern-input"
                           placeholder="أدخل كلمة المرور">
                    <div class="input-group-append">
                        <button type="button" id="togglePassword" class="btn btn-outline-secondary toggle-password">
                            <i id="togglePasswordIcon" class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                <span class="help-block"></span>
            </div>

            <div class="form-group mb-4">
                <div class="custom-control custom-checkbox">
                    <?= form_checkbox('remember', '', FALSE, 'id="remember" class="custom-control-input"');?>
                    <label class="custom-control-label" for="remember">
                        <i class="fas fa-heart" style="color: #e74c3c; margin-left: 5px;"></i>
                        تذكرني
                    </label>
                </div>
            </div>

            <div class="form-group">
                <button type="submit" id="submit" class="btn btn-primary btn-block modern-btn">
                    <i class="fas fa-sign-in-alt" style="margin-left: 8px;"></i>
                    تسجيل الدخول
                </button>
            </div>

            <?= form_close(); ?>

            <div class="login-footer">
                <p class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt" style="margin-left: 5px;"></i>
                        محمي بأحدث تقنيات الأمان
                    </small>
                </p>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="<?=base_url()?>assets/bower_components/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

    <script type="text/javascript">
        let base_url = '<?=base_url();?>';
        let index = new URLSearchParams(window.location.search).get('index');

        document.addEventListener('DOMContentLoaded', function() {
            // تحسين تجربة المستخدم
            addInputAnimations();
            setupSavedIdentities();
            setupPasswordToggle();
            setupFormValidation();
        });

        function addInputAnimations() {
            const inputs = document.querySelectorAll('.modern-input');

            inputs.forEach(input => {
                // تأثير التركيز
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                    this.parentElement.style.transition = 'transform 0.3s ease';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });

                // تأثير الكتابة
                input.addEventListener('input', function() {
                    if (this.value.length > 0) {
                        this.style.background = 'white';
                        this.style.borderColor = '#2ecc71';
                    } else {
                        this.style.background = 'rgba(255, 255, 255, 0.9)';
                        this.style.borderColor = '#e9ecef';
                    }
                });
            });
        }

        function setupSavedIdentities() {
            let savedData = JSON.parse(localStorage.getItem('savedData')) || [];
            let savedIdentitiesSelect = document.getElementById('savedIdentitiesSelect');
            let identityField = document.getElementById('identity');
            let passwordField = document.getElementById('password');

            // إظهار القائمة فقط إذا كان هناك بيانات محفوظة
            if (savedData.length > 0) {
                savedIdentitiesSelect.style.display = 'block';

                // إضافة كل بريد إلكتروني إلى قائمة الاختيار
                savedData.forEach(function(data) {
                    let option = document.createElement('option');
                    option.value = data.identity;
                    option.textContent = data.identity;
                    savedIdentitiesSelect.appendChild(option);
                });

                // عند اختيار بريد إلكتروني من القائمة
                savedIdentitiesSelect.addEventListener('change', function() {
                    let selectedData = savedData.find(data => data.identity === savedIdentitiesSelect.value);
                    if (selectedData) {
                        identityField.value = selectedData.identity;
                        passwordField.value = selectedData.password;
                        identityField.dispatchEvent(new Event('input')); // تفعيل تأثير الكتابة
                        passwordField.dispatchEvent(new Event('input'));
                    } else {
                        identityField.value = '';
                        passwordField.value = '';
                    }
                });
            }
        }

        function setupPasswordToggle() {
            let togglePasswordButton = document.getElementById('togglePassword');
            let togglePasswordIcon = document.getElementById('togglePasswordIcon');
            let passwordField = document.getElementById('password');

            togglePasswordButton.addEventListener('click', function() {
                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    togglePasswordIcon.className = 'fas fa-eye-slash';
                    this.style.background = '#667eea';
                    this.style.color = 'white';
                } else {
                    passwordField.type = 'password';
                    togglePasswordIcon.className = 'fas fa-eye';
                    this.style.background = 'rgba(255, 255, 255, 0.9)';
                    this.style.color = '#667eea';
                }
            });
        }

        function setupFormValidation() {
            const submitBtn = document.getElementById('submit');

            // تأثير زر الإرسال
            submitBtn.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 10px 25px rgba(102, 126, 234, 0.3)';
            });

            submitBtn.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });
        }

        // تأثيرات إضافية عند تحميل الصفحة
        window.addEventListener('load', function() {
            const loginBox = document.querySelector('.login-box-body');
            loginBox.style.opacity = '0';
            loginBox.style.transform = 'translateY(30px)';

            setTimeout(() => {
                loginBox.style.transition = 'all 0.6s ease';
                loginBox.style.opacity = '1';
                loginBox.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>

    <script src="<?=base_url()?>assets/dist/js/app/auth/login.js"></script>
</body>
</html>
