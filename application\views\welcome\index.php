<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?></title>
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎓</text></svg>">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="<?=base_url()?>assets/bower_components/bootstrap/dist/css/bootstrap.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="<?=base_url()?>assets/bower_components/font-awesome/css/font-awesome.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?=base_url()?>assets/dist/css/font-fix.css">
    <link rel="stylesheet" href="<?=base_url()?>assets/dist/css/welcome.css">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        
        .hero-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .hero-content {
            text-align: center;
            color: white;
            z-index: 2;
            max-width: 800px;
            padding: 40px 20px;
        }
        
        .hero-title {
            font-size: 3.5rem;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: fadeInUp 1s ease-out;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 30px;
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.2s both;
        }
        
        .hero-description {
            font-size: 1.1rem;
            margin-bottom: 40px;
            opacity: 0.8;
            line-height: 1.6;
            animation: fadeInUp 1s ease-out 0.4s both;
        }
        
        .btn-group-custom {
            animation: fadeInUp 1s ease-out 0.6s both;
        }
        
        .btn-custom {
            padding: 15px 30px;
            font-size: 1.1rem;
            border-radius: 50px;
            margin: 10px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            min-width: 180px;
        }
        
        .btn-primary-custom {
            background: rgba(255,255,255,0.2);
            border: 2px solid white;
            color: white;
            backdrop-filter: blur(10px);
        }
        
        .btn-primary-custom:hover {
            background: white;
            color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .btn-secondary-custom {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.5);
            color: white;
            backdrop-filter: blur(10px);
        }
        
        .btn-secondary-custom:hover {
            background: rgba(255,255,255,0.2);
            border-color: white;
            transform: translateY(-2px);
        }
        
        .features-section {
            background: white;
            padding: 80px 0;
        }
        
        .feature-card {
            text-align: center;
            padding: 40px 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            transition: transform 0.3s ease;
            background: white;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 20px;
        }
        
        .feature-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .feature-description {
            color: #666;
            line-height: 1.6;
        }
        
        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }
        
        .shape {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            top: 80%;
            left: 20%;
            animation-delay: 4s;
        }

        .shape:nth-child(4) {
            width: 100px;
            height: 100px;
            top: 30%;
            right: 20%;
            animation-delay: 1s;
        }

        .shape:nth-child(5) {
            width: 40px;
            height: 40px;
            top: 10%;
            right: 30%;
            animation-delay: 3s;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.1rem;
            }
            
            .btn-custom {
                display: block;
                margin: 10px auto;
                max-width: 250px;
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
        
        <div class="hero-content">
            <h1 class="hero-title">
                <i class="fa fa-graduation-cap"></i>
                نظام الامتحانات الذكي
            </h1>
            <h2 class="hero-subtitle">SMART Examination System</h2>
            <p class="hero-description">
                <?= $description ?><br>
                منصة متطورة تدعم إنشاء وإدارة الامتحانات الإلكترونية بسهولة وأمان
            </p>
            
            <div class="btn-group-custom">
                <a href="<?= base_url('auth') ?>" class="btn-custom btn-primary-custom">
                    <i class="fa fa-sign-in"></i> تسجيل الدخول
                </a>
                <a href="#features" class="btn-custom btn-secondary-custom">
                    <i class="fa fa-info-circle"></i> المزيد من المعلومات
                </a>
            </div>
        </div>

        <div class="scroll-indicator">
            <i class="fa fa-chevron-down"></i>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features-section">
        <div class="container">
            <div class="row">
                <div class="col-md-12 text-center">
                    <h2 style="margin-bottom: 50px; color: #333; font-size: 2.5rem;">مميزات النظام</h2>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fa fa-shield"></i>
                        </div>
                        <h3 class="feature-title">أمان عالي</h3>
                        <p class="feature-description">
                            نظام حماية متقدم يضمن سرية الامتحانات ومنع الغش الإلكتروني
                        </p>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fa fa-clock-o"></i>
                        </div>
                        <h3 class="feature-title">إدارة الوقت</h3>
                        <p class="feature-description">
                            تحكم كامل في أوقات الامتحانات مع عداد تنازلي وإنهاء تلقائي
                        </p>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fa fa-bar-chart"></i>
                        </div>
                        <h3 class="feature-title">تقارير مفصلة</h3>
                        <p class="feature-description">
                            إحصائيات شاملة ونتائج فورية مع تحليل أداء الطلاب
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fa fa-mobile"></i>
                        </div>
                        <h3 class="feature-title">متوافق مع الجوال</h3>
                        <p class="feature-description">
                            يعمل على جميع الأجهزة والمتصفحات بتصميم متجاوب
                        </p>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fa fa-users"></i>
                        </div>
                        <h3 class="feature-title">إدارة المستخدمين</h3>
                        <p class="feature-description">
                            نظام صلاحيات متقدم للطلاب والمعلمين والإداريين
                        </p>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fa fa-cogs"></i>
                        </div>
                        <h3 class="feature-title">سهولة الاستخدام</h3>
                        <p class="feature-description">
                            واجهة بسيطة وسهلة الاستخدام لجميع فئات المستخدمين
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer style="background: #333; color: white; padding: 40px 0; text-align: center;">
        <div class="container">
            <p style="margin: 0; font-size: 1.1rem;">
                <strong>نظام الامتحانات الذكي</strong> - تطوير: مصطفى نادي صابر
            </p>
            <p style="margin: 10px 0 0 0; opacity: 0.7;">
                جميع الحقوق محفوظة © <?= date('Y') ?>
            </p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="<?=base_url()?>assets/bower_components/jquery/jquery-3.3.1.min.js"></script>
    <script src="<?=base_url()?>assets/bower_components/bootstrap/dist/js/bootstrap.min.js"></script>
    
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add scroll animation for feature cards
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe feature cards
        document.addEventListener('DOMContentLoaded', () => {
            const featureCards = document.querySelectorAll('.feature-card');
            featureCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
